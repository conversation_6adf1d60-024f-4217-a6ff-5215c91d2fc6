name: Deploy Filtro Curricular to Azure

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/filtro-curricular/**'
      - 'apps/infrastructure/azure/filtro-curricular/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/filtro-curricular/**'
      - 'apps/infrastructure/azure/filtro-curricular/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod

env:
  TERRAFORM_VERSION: '1.5.0'
  WORKING_DIRECTORY: 'apps/infrastructure/azure/filtro-curricular'

jobs:
  terraform-plan:
    name: 'Terraform Plan'
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    
    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: ${{ env.TERRAFORM_VERSION }}
    
    - name: Azure Login
      uses: azure/login@v2
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
        enable-AzPSSession: true
        environment: azurecloud
        allow-no-subscriptions: false
        audience: api://AzureADTokenExchange
        auth-type: SERVICE_PRINCIPAL

    - name: Create terraform.tfvars
      run: |
        cat > terraform.tfvars << EOF
        project_name = "filtro-curricular"
        environment  = "${{ github.event.inputs.environment || 'dev' }}"
        location     = "${{ vars.AZURE_LOCATION || 'Chile Central' }}"
        
        # OpenAI Configuration
        openai_api_key        = "${{ secrets.OPENAI_API_KEY }}"
        openai_token          = "${{ secrets.OPENAI_TOKEN }}"
        assistant_id_juridico = "${{ secrets.ASSISTANT_ID_JURIDICO }}"
        assistant_id_calidad  = "${{ secrets.ASSISTANT_ID_CALIDAD }}"
        
        # App Service Configuration
        app_service_plan_sku = "${{ vars.APP_SERVICE_PLAN_SKU || 'B1' }}"
        acr_sku             = "${{ vars.ACR_SKU || 'Basic' }}"
        
        tags = {
          Project     = "filtro-curricular"
          Environment = "${{ github.event.inputs.environment || 'dev' }}"
          ManagedBy   = "github-actions"
          Repository  = "${{ github.repository }}"
        }
        EOF
    
    - name: Terraform Init
      run: terraform init
    
    - name: Terraform Validate
      run: terraform validate
    
    - name: Terraform Plan
      run: terraform plan -out=tfplan
    
    - name: Upload Terraform Plan
      uses: actions/upload-artifact@v4
      with:
        name: terraform-plan
        path: ${{ env.WORKING_DIRECTORY }}/tfplan
        retention-days: 1

  terraform-apply:
    name: 'Terraform Apply'
    runs-on: ubuntu-latest
    needs: terraform-plan
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop' || github.event_name == 'workflow_dispatch'
    environment: ${{ github.event.inputs.environment || 'dev' }}
    
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    
    outputs:
      acr_name: ${{ steps.terraform-output.outputs.acr_name }}
      acr_login_server: ${{ steps.terraform-output.outputs.acr_login_server }}
      resource_group_name: ${{ steps.terraform-output.outputs.resource_group_name }}
      backend_app_name: ${{ steps.terraform-output.outputs.backend_app_name }}
      frontend_app_name: ${{ steps.terraform-output.outputs.frontend_app_name }}
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    
    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: ${{ env.TERRAFORM_VERSION }}
        terraform_wrapper: false
    
    - name: Azure Login
      uses: azure/login@v2
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
        enable-AzPSSession: true
        environment: azurecloud
        allow-no-subscriptions: false
        audience: api://AzureADTokenExchange
        auth-type: SERVICE_PRINCIPAL

    - name: Create terraform.tfvars
      run: |
        cat > terraform.tfvars << EOF
        project_name = "filtro-curricular"
        environment  = "${{ github.event.inputs.environment || 'dev' }}"
        location     = "${{ vars.AZURE_LOCATION || 'Chile Central' }}"
        
        # OpenAI Configuration
        openai_api_key        = "${{ secrets.OPENAI_API_KEY }}"
        openai_token          = "${{ secrets.OPENAI_TOKEN }}"
        assistant_id_juridico = "${{ secrets.ASSISTANT_ID_JURIDICO }}"
        assistant_id_calidad  = "${{ secrets.ASSISTANT_ID_CALIDAD }}"
        
        # App Service Configuration
        app_service_plan_sku = "${{ vars.APP_SERVICE_PLAN_SKU || 'B1' }}"
        acr_sku             = "${{ vars.ACR_SKU || 'Basic' }}"
        
        tags = {
          Project     = "filtro-curricular"
          Environment = "${{ github.event.inputs.environment || 'dev' }}"
          ManagedBy   = "github-actions"
          Repository  = "${{ github.repository }}"
        }
        EOF
    
    - name: Download Terraform Plan
      uses: actions/download-artifact@v4
      with:
        name: terraform-plan
        path: ${{ env.WORKING_DIRECTORY }}
    
    - name: Terraform Init
      run: terraform init
    
    - name: Terraform Apply
      run: terraform apply tfplan
    
    - name: Get Terraform Outputs
      id: terraform-output
      run: |
        echo "acr_name=$(terraform output -raw container_registry_name)" >> $GITHUB_OUTPUT
        echo "acr_login_server=$(terraform output -raw container_registry_login_server)" >> $GITHUB_OUTPUT
        echo "resource_group_name=$(terraform output -raw resource_group_name)" >> $GITHUB_OUTPUT
        echo "backend_app_name=$(terraform output -raw backend_app_service_name)" >> $GITHUB_OUTPUT
        echo "frontend_app_name=$(terraform output -raw frontend_app_service_name)" >> $GITHUB_OUTPUT

  build-and-deploy:
    name: 'Build and Deploy Applications'
    runs-on: ubuntu-latest
    needs: terraform-apply
    environment: ${{ github.event.inputs.environment || 'dev' }}
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    
    - name: Azure Login
      uses: azure/login@v2
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
        enable-AzPSSession: true
        environment: azurecloud
        allow-no-subscriptions: false
        audience: api://AzureADTokenExchange
        auth-type: SERVICE_PRINCIPAL

    - name: Login to Azure Container Registry
      run: az acr login --name ${{ needs.terraform-apply.outputs.acr_name }}
    
    - name: Build and Push Backend Image
      run: |
        cd apps/filtro-curricular/filtro-curricular-be-api
        docker build -t ${{ needs.terraform-apply.outputs.acr_login_server }}/filtro-curricular-be-api:${{ github.sha }} .
        docker build -t ${{ needs.terraform-apply.outputs.acr_login_server }}/filtro-curricular-be-api:latest .
        docker push ${{ needs.terraform-apply.outputs.acr_login_server }}/filtro-curricular-be-api:${{ github.sha }}
        docker push ${{ needs.terraform-apply.outputs.acr_login_server }}/filtro-curricular-be-api:latest
    
    - name: Build and Push Frontend Image
      run: |
        cd apps/filtro-curricular/filtro-curricular-fe-web
        docker build -t ${{ needs.terraform-apply.outputs.acr_login_server }}/filtro-curricular-fe-web:${{ github.sha }} .
        docker build -t ${{ needs.terraform-apply.outputs.acr_login_server }}/filtro-curricular-fe-web:latest .
        docker push ${{ needs.terraform-apply.outputs.acr_login_server }}/filtro-curricular-fe-web:${{ github.sha }}
        docker push ${{ needs.terraform-apply.outputs.acr_login_server }}/filtro-curricular-fe-web:latest
    
    - name: Restart Backend App Service
      run: |
        az webapp restart \
          --name ${{ needs.terraform-apply.outputs.backend_app_name }} \
          --resource-group ${{ needs.terraform-apply.outputs.resource_group_name }}
    
    - name: Restart Frontend App Service
      run: |
        az webapp restart \
          --name ${{ needs.terraform-apply.outputs.frontend_app_name }} \
          --resource-group ${{ needs.terraform-apply.outputs.resource_group_name }}
    
    - name: Deployment Summary
      run: |
        echo "## Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Environment**: ${{ github.event.inputs.environment || 'dev' }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Resource Group**: ${{ needs.terraform-apply.outputs.resource_group_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Container Registry**: ${{ needs.terraform-apply.outputs.acr_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Backend App**: ${{ needs.terraform-apply.outputs.backend_app_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Frontend App**: ${{ needs.terraform-apply.outputs.frontend_app_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit SHA**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
